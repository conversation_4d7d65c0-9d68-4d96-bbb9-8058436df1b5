<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصحح الأسنان البنفسجي - Alfa Smile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --purple: #8e44ad;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* الشريط العلوي */
        .navbar {
            background-color: white;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            height: 50px;
            width: auto;
        }

        .logo-img {
            height: 150px;
            width: auto;
            object-fit: contain;
        }

        .logo h1 {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 700;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: var(--purple);
        }

        .back-btn {
            background: linear-gradient(135deg, var(--purple), #9b59b6);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(142, 68, 173, 0.4);
        }

        /* الصفحة الرئيسية */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            color: white;
            text-align: center;
            margin-top: 80px;
            overflow: hidden;
        }

        .hero-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgb(187, 134, 134), rgba(228, 163, 253, 0.8));
            z-index: -1;
        }

        .hero-content {
            max-width: 800px;
            padding: 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 900;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        /* قسم المنتج */
        .product-section {
            padding: 80px 5%;
            background: white;
        }

        .product-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .product-image {
            text-align: center;
        }

        .main-image {
            width: 100%;
            max-width: 500px;
            height: 500px;
            object-fit: cover;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(142, 68, 173, 0.2);
            margin-bottom: 30px;
        }

        .product-details h2 {
            font-size: 2.5rem;
            color: var(--purple);
            margin-bottom: 20px;
            font-weight: 700;
        }

        .product-details p {
            font-size: 1.2rem;
            color: var(--gray);
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 40px;
        }

        .features-list li {
            padding: 12px 0;
            color: var(--primary);
            font-weight: 500;
            font-size: 1.1rem;
            position: relative;
            padding-right: 30px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: var(--purple);
            font-weight: bold;
            font-size: 1.2rem;
        }

        /* شريط الصور */
        .image-gallery {
            padding: 80px 5%;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--purple);
            display: inline-block;
            padding-bottom: 15px;
            position: relative;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--purple);
        }

        .gallery-slider {
            position: relative;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 20px 0;
            scrollbar-width: thin;
            scrollbar-color: var(--purple) var(--light);
        }

        .gallery-slider::-webkit-scrollbar {
            height: 8px;
        }

        .gallery-slider::-webkit-scrollbar-track {
            background: var(--light);
            border-radius: 4px;
        }

        .gallery-slider::-webkit-scrollbar-thumb {
            background: var(--purple);
            border-radius: 4px;
        }

        .gallery-container {
            display: flex;
            gap: 20px;
            width: max-content;
        }

        .gallery-item {
            min-width: 300px;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(142, 68, 173, 0.2);
            transition: var(--transition);
            cursor: pointer;
        }

        .gallery-item:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(142, 68, 173, 0.3);
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .gallery-item:hover img {
            transform: scale(1.1);
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .product-container {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .main-image {
                height: 300px;
            }
            
            .product-details h2 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="logo">
            <img src="images/WhatsApp_Image_2025-01-22_at_9.46.14_PM-removebg-preview.webp" alt="Alfa Smile Logo" class="logo-img">
        </div>
        <ul class="nav-links">
            <li><a href="index.html">الصفحة الرئيسية</a></li>
            <li><a href="index.html#products">المنتجات</a></li>
            <li><a href="index.html#contact">تواصل معنا</a></li>
        </ul>
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>
    </nav>

    <!-- الصفحة الرئيسية -->
    <section class="hero">
        <video class="hero-video" autoplay muted loop>
            <source src="video/Alfa Smile Dental Clinic.mp4" type="video/mp4">
        </video>
       <!-- <div class="hero-overlay"></div>
       <div class="hero-content">
            <h1>مصحح الأسنان البنفسجي</h1>
            <p>منتج ثوري لتصحيح لون الأسنان وإزالة الاصفرار. تقنية الألوان المتضادة لنتائج فورية ومذهلة.</p>
        </div>-->
    </section>

    <!-- قسم المنتج -->
    <section class="product-section">
        <div class="product-container">
            <div class="product-image">
                <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي" class="main-image">
            </div>
            <div class="product-details">
                <h2>مصحح الأسنان البنفسجي</h2>
                <p>
                    منتج مبتكر يستخدم تقنية الألوان المتضادة لتصحيح لون الأسنان فوراً.
                    اللون البنفسجي يعادل الاصفرار ويعطي مظهراً أبيض ناصع للأسنان خلال دقائق معدودة.
                </p>
                <ul class="features-list">
                    <li>تأثير فوري خلال 30 ثانية</li>
                    <li>تقنية الألوان المتضادة المتطورة</li>
                    <li>سهل الاستخدام في المنزل</li>
                    <li>مكونات طبيعية آمنة 100%</li>
                    <li>بدون مواد كيميائية ضارة</li>
                    <li>مناسب للاستخدام اليومي</li>
                    <li>نتائج تدوم حتى 24 ساعة</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- معرض الصور -->
    <section class="image-gallery">
        <div class="section-title">
            <h2>معرض الصور</h2>
        </div>
        <div class="gallery-slider">
            <div class="gallery-container">
                <div class="gallery-item">
                    <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي 1">
                </div>
                <div class="gallery-item">
                    <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي 2">
                </div>
                <div class="gallery-item">
                    <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي 3">
                </div>
                <div class="gallery-item">
                    <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي 4">
                </div>
                <div class="gallery-item">
                    <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي 5">
                </div>
            </div>
        </div>
    </section>

    <script>
        // إعداد التمرير السلس للمعرض
        document.addEventListener('DOMContentLoaded', () => {
            const gallerySlider = document.querySelector('.gallery-slider');

            if (gallerySlider) {
                // إضافة دعم التمرير بالماوس
                let isDown = false;
                let startX;
                let scrollLeft;

                gallerySlider.addEventListener('mousedown', (e) => {
                    isDown = true;
                    gallerySlider.style.cursor = 'grabbing';
                    startX = e.pageX - gallerySlider.offsetLeft;
                    scrollLeft = gallerySlider.scrollLeft;
                });

                gallerySlider.addEventListener('mouseleave', () => {
                    isDown = false;
                    gallerySlider.style.cursor = 'grab';
                });

                gallerySlider.addEventListener('mouseup', () => {
                    isDown = false;
                    gallerySlider.style.cursor = 'grab';
                });

                gallerySlider.addEventListener('mousemove', (e) => {
                    if (!isDown) return;
                    e.preventDefault();
                    const x = e.pageX - gallerySlider.offsetLeft;
                    const walk = (x - startX) * 2;
                    gallerySlider.scrollLeft = scrollLeft - walk;
                });

                // إضافة دعم التمرير بالعجلة
                gallerySlider.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    gallerySlider.scrollLeft += e.deltaY;
                });

                // تعيين مؤشر اليد
                gallerySlider.style.cursor = 'grab';
            }
        });
    </script>
</body>
</html>
