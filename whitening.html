<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تبييض الأسنان المتقدم - Alfa Smile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* الشريط العلوي */
        .navbar {
            background-color: white;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            height: 50px;
            width: auto;
        }

        .logo-img {
            height: 150px;
            width: auto;
            object-fit: contain;
        }

        .logo h1 {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 700;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: #00d4ff;
        }

        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 212, 255, 0.4);
        }

        /* الصفحة الرئيسية */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            color: white;
            text-align: center;
            margin-top: 80px;
            overflow: hidden;
        }

        .hero-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.7), rgba(0, 153, 204, 0.8));
            z-index: -1;
        }

        .hero-content {
            max-width: 800px;
            padding: 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 900;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        /* قسم المنتج */
        .product-section {
            padding: 80px 5%;
            background: white;
        }

        .product-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .product-image {
            text-align: center;
            
        }

        .main-image {
            width: 100%;
            max-width: 500px;
            height: 500px;
            object-fit: cover;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .product-details h2 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 20px;
            font-weight: 700;
        }

        .product-details p {
            font-size: 1.2rem;
            color: var(--gray);
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 40px;
        }

        .features-list li {
            padding: 12px 0;
            color: var(--primary);
            font-weight: 500;
            font-size: 1.1rem;
            position: relative;
            padding-right: 30px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #00d4ff;
            font-weight: bold;
            font-size: 1.2rem;
        }

        /* شريط الصور */
        .image-gallery {
            padding: 80px 5%;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--primary);
            display: inline-block;
            padding-bottom: 15px;
            position: relative;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #00d4ff;
        }

        .gallery-slider {
            position: relative;
            overflow: hidden;
            padding: 20px 0;
            margin: 0 60px;
        }

        .gallery-wrapper {
            position: relative;
            overflow-x: auto;
            overflow-y: hidden;
            scrollbar-width: thin;
            scrollbar-color: #00d4ff var(--light);
            scroll-behavior: smooth;
        }

        .gallery-wrapper::-webkit-scrollbar {
            height: 8px;
        }

        .gallery-wrapper::-webkit-scrollbar-track {
            background: var(--light);
            border-radius: 4px;
        }

        .gallery-wrapper::-webkit-scrollbar-thumb {
            background: #00d4ff;
            border-radius: 4px;
        }

        /* أزرار التنقل للمعرض */
        .gallery-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: var(--transition);
            z-index: 10;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gallery-nav-btn:hover {
            background: linear-gradient(135deg, #0099cc, #007399);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
        }

        .gallery-prev {
            right: 10px;
        }

        .gallery-next {
            left: 10px;
        }

        .gallery-container {
            display: flex;
            gap: 20px;
            width: max-content;
        }

        .gallery-item {
            min-width: 300px;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            cursor: pointer;
        }

        .gallery-item:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .gallery-item:hover img {
            transform: scale(1.1);
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .product-container {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .main-image {
                height: 300px;
            }
            
            .product-details h2 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="logo">
            <img src="images/WhatsApp_Image_2025-01-22_at_9.46.14_PM-removebg-preview.webp" alt="Alfa Smile Logo" class="logo-img">
          <!--  <h1>Alfa Smile</h1>-->
        </div>
        <ul class="nav-links">
            <li><a href="index.html">الصفحة الرئيسية</a></li>
            <li><a href="index.html#products">المنتجات</a></li>
            <li><a href="index.html#contact">تواصل معنا</a></li>
        </ul>
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>
    </nav>

    <!-- الصفحة الرئيسية -->
    <section class="hero">
        <video class="hero-video" autoplay muted loop>
            <source src="video/Alfa Smile Dental Clinic.mp4" type="video/mp4">
        </video>
        <!--<div class="hero-overlay"></div>
        <div class="hero-content">
            <h1>تبييض الأسنان المتقدم</h1>
            <p>تقنية ثورية لتبييض الأسنان بأمان وفعالية. احصل على ابتسامة مشرقة وثقة لا محدودة مع أحدث التقنيات العالمية.</p>
        </div>-->
    </section>

    <!-- قسم المنتج -->
    <section class="product-section">
        <div class="product-container">
            <div class="product-image">
                <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان المتقدم" class="main-image">
            </div>
            <div class="product-details">
                <h2>تبييض الأسنان المتقدم</h2>
                <p>
                    نقدم لك أحدث تقنيات تبييض الأسنان المتطورة التي تضمن نتائج مذهلة وآمنة تماماً.
                    تقنيتنا المبتكرة تعمل على إزالة التصبغات العميقة وتعيد للأسنان بياضها الطبيعي.
                </p>
                <ul class="features-list">
                    <li>نتائج فورية خلال جلسة واحدة فقط</li>
                    <li>آمن 100% على مينا الأسنان واللثة</li>
                    <li>تقنية LED المتطورة للتفعيل</li>
                    <li>ضمان النتائج لمدة سنة كاملة</li>
                    <li>بدون ألم أو حساسية</li>
                    <li>مواد طبيعية معتمدة عالمياً</li>
                    <li>نتائج تدوم حتى 3 سنوات</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- معرض الصور -->
    <section class="image-gallery">
        <div class="gallery-slider">
            <button class="gallery-nav-btn gallery-prev" id="galleryPrev">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="gallery-nav-btn gallery-next" id="galleryNext">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="gallery-wrapper" id="galleryWrapper">
                <div class="gallery-container">
                    <div class="gallery-item">
                        <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان 1">
                    </div>
                    <div class="gallery-item">
                        <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان 2">
                    </div>
                    <div class="gallery-item">
                        <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان 3">
                    </div>
                    <div class="gallery-item">
                        <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان 4">
                    </div>
                    <div class="gallery-item">
                        <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان 5">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // إعداد التمرير السلس للمعرض
        document.addEventListener('DOMContentLoaded', () => {
            const galleryWrapper = document.getElementById('galleryWrapper');
            const galleryContainer = document.querySelector('.gallery-container');
            const prevBtn = document.getElementById('galleryPrev');
            const nextBtn = document.getElementById('galleryNext');

            if (!galleryWrapper || !galleryContainer || !prevBtn || !nextBtn) return;

            // متغيرات التحكم
            let currentIndex = 0;
            let itemWidth = 320; // عرض العنصر + المسافة
            let visibleItems = Math.floor(galleryWrapper.offsetWidth / itemWidth);

            // تحديث عرض العنصر
            function updateItemWidth() {
                const items = galleryContainer.children;
                if (items.length > 0) {
                    itemWidth = items[0].offsetWidth + 20;
                    visibleItems = Math.floor(galleryWrapper.offsetWidth / itemWidth);
                    updateGalleryPosition();
                    updateButtonStates();
                }
            }

            // تحديث موقع المعرض
            function updateGalleryPosition() {
                const maxScroll = Math.max(0, (galleryContainer.children.length * itemWidth) - galleryWrapper.offsetWidth);
                const scrollPosition = Math.min(currentIndex * itemWidth, maxScroll);
                galleryWrapper.scrollTo({
                    left: scrollPosition,
                    behavior: 'smooth'
                });
            }

            // تحديث حالة الأزرار
            function updateButtonStates() {
                const maxIndex = Math.max(0, galleryContainer.children.length - visibleItems);
                prevBtn.disabled = currentIndex <= 0;
                nextBtn.disabled = currentIndex >= maxIndex;
            }

            // التنقل للسابق
            prevBtn.addEventListener('click', () => {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateGalleryPosition();
                    updateButtonStates();
                }
            });

            // التنقل للتالي
            nextBtn.addEventListener('click', () => {
                const maxIndex = Math.max(0, galleryContainer.children.length - visibleItems);
                if (currentIndex < maxIndex) {
                    currentIndex++;
                    updateGalleryPosition();
                    updateButtonStates();
                }
            });

            // إضافة دعم التمرير بالماوس
            let isDown = false;
            let startX;
            let scrollLeft;

            galleryWrapper.addEventListener('mousedown', (e) => {
                isDown = true;
                galleryWrapper.style.cursor = 'grabbing';
                startX = e.pageX - galleryWrapper.offsetLeft;
                scrollLeft = galleryWrapper.scrollLeft;
            });

            galleryWrapper.addEventListener('mouseleave', () => {
                isDown = false;
                galleryWrapper.style.cursor = 'grab';
            });

            galleryWrapper.addEventListener('mouseup', () => {
                isDown = false;
                galleryWrapper.style.cursor = 'grab';
            });

            galleryWrapper.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - galleryWrapper.offsetLeft;
                const walk = (x - startX) * 2;
                galleryWrapper.scrollLeft = scrollLeft - walk;
            });

            // إضافة دعم التمرير بالعجلة
            galleryWrapper.addEventListener('wheel', (e) => {
                e.preventDefault();
                galleryWrapper.scrollLeft += e.deltaY;
            });

            // تحديث عند تغيير حجم النافذة
            window.addEventListener('resize', updateItemWidth);

            // تعيين مؤشر اليد
            galleryWrapper.style.cursor = 'grab';

            // تحديث أولي
            setTimeout(updateItemWidth, 100);
        });
    </script>
</body>
</html>
