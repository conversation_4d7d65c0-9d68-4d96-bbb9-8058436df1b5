<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --success: #27ae60;
            --warning: #f39c12;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* صفحة تسجيل الدخول */
        .login-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            padding: 20px;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-logo {
            margin-bottom: 30px;
        }

        .login-logo i {
            font-size: 4rem;
            color: var(--secondary);
            margin-bottom: 15px;
        }

        .login-logo h1 {
            color: var(--primary);
            font-size: 1.8rem;
            font-weight: 700;
        }

        .login-form {
            text-align: right;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark);
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            transition: var(--transition);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--secondary);
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: var(--secondary);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-family: 'Cairo', sans-serif;
        }

        .login-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .error-message {
            background: #ffe6e6;
            color: var(--accent);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ffcccb;
            display: none;
        }

        /* لوحة التحكم الرئيسية */
        .admin-dashboard {
            display: none;
            min-height: 100vh;
        }

        .admin-header {
            background: white;
            padding: 20px 30px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .admin-header h1 {
            color: var(--primary);
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-header h1 i {
            color: var(--secondary);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: var(--accent);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            font-family: 'Cairo', sans-serif;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .main-content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.products { background: var(--secondary); }
        .stat-icon.sales { background: var(--success); }
        .stat-icon.orders { background: var(--warning); }
        .stat-icon.users { background: var(--accent); }

        .stat-info h3 {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-info p {
            color: var(--gray);
            font-weight: 600;
        }

        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .card-header {
            background: var(--primary);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 25px;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: var(--secondary);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success);
        }

        .btn-success:hover {
            background: #219a52;
        }

        .btn-danger {
            background: var(--accent);
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: var(--warning);
        }

        .btn-warning:hover {
            background: #d68910;
        }

        /* نموذج إضافة المنتج */
        .product-form {
            display: grid;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark);
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--secondary);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .image-preview {
            width: 150px;
            height: 150px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            overflow: hidden;
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-preview.empty {
            color: var(--gray);
            font-size: 0.9rem;
            text-align: center;
        }

        /* جدول المنتجات */
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .products-table th,
        .products-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }

        .products-table th {
            background: var(--light);
            font-weight: 700;
            color: var(--primary);
            position: sticky;
            top: 0;
        }

        .products-table tr:hover {
            background: #f9f9f9;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }

        .product-name {
            font-weight: 600;
            color: var(--primary);
        }

        .product-price {
            font-weight: 700;
            color: var(--success);
            font-size: 1.1rem;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .edit-btn {
            background: var(--warning);
            color: white;
        }

        .delete-btn {
            background: var(--accent);
            color: white;
        }

        .view-btn {
            background: var(--secondary);
            color: white;
        }

        /* رسائل التنبيه */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 10px;
        }

        .alert.success {
            background: #d4f6d4;
            color: var(--success);
            border: 1px solid #c3e6c3;
        }

        .alert.error {
            background: #ffe6e6;
            color: var(--accent);
            border: 1px solid #ffcccb;
        }

        .alert.show {
            display: flex;
        }

        /* نافذة منبثقة للتأكيد */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .modal-content h3 {
            color: var(--primary);
            margin-bottom: 15px;
        }

        .modal-content p {
            color: var(--gray);
            margin-bottom: 25px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .main-content {
                padding: 15px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .products-table {
                font-size: 0.9rem;
            }

            .products-table th,
            .products-table td {
                padding: 10px 8px;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: var(--gray);
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- صفحة تسجيل الدخول -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="login-logo">
                <i class="fas fa-shield-alt"></i>
                <h1>لوحة التحكم الإدارية</h1>
                <p>إدارة المنتجات والمتجر الإلكتروني</p>
            </div>
            
            <div id="errorMessage" class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <span>اسم المستخدم أو كلمة المرور غير صحيحة</span>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required>
                    <i class="fas fa-user"></i>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                    <i class="fas fa-lock"></i>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول
                </button>
            </form>
            
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 0.9rem; color: var(--gray);">
                <strong>بيانات الدخول التجريبية:</strong><br>
                اسم المستخدم: admin<br>
                كلمة المرور: admin123
            </div>
        </div>
    </div>

    <!-- لوحة التحكم الرئيسية -->
    <div id="adminDashboard" class="admin-dashboard">
        <!-- رأس الصفحة -->
        <header class="admin-header">
            <h1>
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم الإدارية
            </h1>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <span>مرحباً، المدير</span>
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalProducts">0</h3>
                        <p>إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon sales">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3>1,250</h3>
                        <p>إجمالي المبيعات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3>84</h3>
                        <p>الطلبات الجديدة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3>342</h3>
                        <p>العملاء المسجلون</p>
                    </div>
                </div>
            </div>

            <!-- رسائل التنبيه -->
            <div id="alertContainer"></div>

            <!-- نموذج إضافة منتج جديد -->
            <div class="admin-card">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-plus-circle"></i>
                        إضافة منتج جديد
                    </h2>
                    <button type="button" id="clearFormBtn" class="btn btn-warning">
                        <i class="fas fa-eraser"></i>
                        مسح الحقول
                    </button>
                </div>
                <div class="card-body">
                    <form id="productForm" class="product-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productName">اسم المنتج *</label>
                                <input type="text" id="productName" name="productName" required>
                            </div>
                            <div class="form-group">
                                <label for="productPrice">السعر (ر.س) *</label>
                                <input type="number" id="productPrice" name="productPrice" min="0" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productCategory">الفئة *</label>
                                <select id="productCategory" name="productCategory" required>
                                    <option value="">اختر فئة المنتج</option>
                                    <option value="electronics">الكترونيات</option>
                                    <option value="computers">أجهزة كمبيوتر</option>
                                    <option value="phones">هواتف ذكية</option>
                                    <option value="accessories">ملحقات</option>
                                    <option value="home">أجهزة منزلية</option>
                                    <option value="games">ألعاب</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="productImage">رابط صورة المنتج *</label>
                                <input type="url" id="productImage" name="productImage" placeholder="https://example.com/image.jpg" required>
                                <div id="imagePreview" class="image-preview empty">
                                    <span>معاينة الصورة</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="productDescription">وصف المنتج *</label>
                            <textarea id="productDescription" name="productDescription" placeholder="اكتب وصفاً مفصلاً للمنتج..." required></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productStock">الكمية المتوفرة</label>
                                <input type="number" id="productStock" name="productStock" min="0" value="1">
                            </div>
                            <div class="form-group">
                                <label for="productDiscount">نسبة الخصم (%)</label>
                                <input type="number" id="productDiscount" name="productDiscount" min="0" max="100" value="0">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i>
                            حفظ المنتج
                        </button>
                    </form>
                </div>
            </div>

            <!-- قائمة المنتجات -->
            <div class="admin-card">
                <div class="card-header">
                    <h2>
                        <i class="fas fa-list"></i>
                        قائمة المنتجات المضافة
                    </h2>
                    <button type="button" id="refreshBtn" class="btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
                <div class="card-body">
                    <div id="loading" class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>جاري تحميل المنتجات...</p>
                    </div>
                    
                    <div id="productsTableContainer">
                        <table class="products-table">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الخصم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- سيتم إدراج المنتجات هنا ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <h3>تأكيد الحذف</h3>
            <p>هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.</p>
            <div class="modal-buttons">
                <button id="confirmDelete" class="btn btn-danger">حذف</button>
                <button id="cancelDelete" class="btn">إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        const ADMIN_CREDENTIALS = {
            username: 'admin',
            password: 'admin123'
        };

        let products = [];
        let editingProductId = null;
        let deleteProductId = null;

        // عناصر DOM
        const loginPage = document.getElementById('loginPage');
        const adminDashboard = document.getElementById('adminDashboard');
        const loginForm = document.getElementById('loginForm');
        const errorMessage = document.getElementById('errorMessage');
        const logoutBtn = document.getElementById('logoutBtn');
        const productForm = document.getElementById('productForm');
        const productsTableBody = document.getElementById('productsTableBody');
        const totalProductsEl = document.getElementById('totalProducts');
        const imagePreview = document.getElementById('imagePreview');
        const productImageInput = document.getElementById('productImage');
        const confirmModal = document.getElementById('confirmModal');
        const alertContainer = document.getElementById('alertContainer');

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            checkLoginStatus();
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تسجيل الدخول
            loginForm.addEventListener('submit', handleLogin);
            logoutBtn.addEventListener('click', handleLogout);

            // إدارة المنتجات
            productForm.addEventListener('submit', handleProductSubmit);
            productImageInput.addEventListener('input', handleImagePreview);
            
            // أزرار أخرى
            document.getElementById('clearFormBtn').addEventListener('click', clearForm);
            document.getElementById('refreshBtn').addEventListener('click', loadProducts);
            
            // نافذة التأكيد
            document.getElementById('confirmDelete').addEventListener('click', confirmDelete);
            document.getElementById('cancelDelete').addEventListener('click', closeModal);
        }

        // التحقق من حالة تسجيل الدخول
        function checkLoginStatus() {
            const isLoggedIn = sessionStorage.getItem('adminLoggedIn');
            if (isLoggedIn === 'true') {
                showDashboard();
            } else {
                showLoginPage();
            }
        }

        // معالج تسجيل الدخول
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
                sessionStorage.setItem('adminLoggedIn', 'true');
                showDashboard();
                showAlert('تم تسجيل الدخول بنجاح', 'success');
            } else {
                errorMessage.style.display = 'block';
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            }
        }

        // معالج تسجيل الخروج
        function handleLogout() {
            sessionStorage.removeItem('adminLoggedIn');
            showLoginPage();
            showAlert('تم تسجيل الخروج بنجاح', 'success');
        }

        // عرض صفحة تسجيل الدخول
        function showLoginPage() {
            loginPage.style.display = 'flex';
            adminDashboard.style.display = 'none';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
        }

        // عرض لوحة التحكم
        function showDashboard() {
            loginPage.style.display = 'none';
            adminDashboard.style.display = 'block';
            updateStats();
        }

        // معالج إرسال نموذج المنتج
        function handleProductSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(productForm);
            const productData = {
                id: editingProductId || Date.now().toString(),
                name: formData.get('productName'),
                price: parseFloat(formData.get('productPrice')),
                category: formData.get('productCategory'),
                image: formData.get('productImage'),
                description: formData.get('productDescription')
                stock: parseInt(formData.get('productStock')),
                discount: parseInt(formData.get('productDiscount'))
            };
            
            if (editingProductId) {
                updateProduct(productData);
            } else {
                addProduct(productData);
            }
        }

