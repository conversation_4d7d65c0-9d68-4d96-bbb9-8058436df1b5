<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر التجارة الإلكترونية - متجرك الإلكتروني الأول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* الشريط العلوي */
        .navbar {
            background-color: white;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo img {
            height: 50px;
        }

        .logo h1 {
            font-size: 1.5rem;
            color: var(--primary);
            font-weight: 700;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: var(--secondary);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 3px;
            background-color: var(--secondary);
            transition: var(--transition);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .admin-btn {
            background: var(--accent) !important;
            color: white !important;
            padding: 8px 20px !important;
            border-radius: 20px !important;
            transition: var(--transition) !important;
        }

        .admin-btn:hover {
            background: #c0392b !important;
            transform: translateY(-2px) !important;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light);
            border-radius: 30px;
            padding: 8px 15px;
            width: 300px;
        }

        .search-bar input {
            border: none;
            background: transparent;
            width: 100%;
            padding: 5px 10px;
            font-size: 1rem;
            outline: none;
        }

        .search-bar button {
            background: transparent;
            border: none;
            color: var(--secondary);
            cursor: pointer;
            font-size: 1.2rem;
        }

        .hamburger {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* الصفحة الرئيسية */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover no-repeat;
            color: white;
            text-align: center;
            margin-top: 80px;
        }

        .hero-content {
            max-width: 800px;
            padding: 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 900;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: var(--secondary);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
        }

        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* شريط المنتجات */
        .products-slider {
            padding: 80px 5%;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--primary);
            display: inline-block;
            padding-bottom: 15px;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--secondary);
        }

        .slider-container {
            position: relative;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 20px 0;
            scrollbar-width: thin;
            scrollbar-color: var(--secondary) var(--light);
        }

        .slider-container::-webkit-scrollbar {
            height: 8px;
        }

        .slider-container::-webkit-scrollbar-track {
            background: var(--light);
            border-radius: 4px;
        }

        .slider-container::-webkit-scrollbar-thumb {
            background: var(--secondary);
            border-radius: 4px;
        }

        .slider-container::-webkit-scrollbar-thumb:hover {
            background: #2980b9;
        }

        .slider {
            display: flex;
            transition: transform 0.3s ease;
            gap: 0;
            width: max-content;
        }

        .slide {
            min-width: 300px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            margin-right: 20px;
            flex-shrink: 0;
            cursor: pointer;
        }

        .slide:last-child {
            margin-right: 0;
        }

        .slide:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .product-img {
            height: 250px;
            overflow: hidden;
        }

        .product-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .slide:hover .product-img img {
            transform: scale(1.1);
        }

        .product-info {
            padding: 20px;
        }

        .product-info h3 {
            font-size: 1.4rem;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .product-info p {
            color: var(--gray);
            margin-bottom: 15px;
            height: 60px;
            overflow: hidden;
        }

        .product-price {
            font-size: 1.5rem;
            color: var(--accent);
            font-weight: 700;
            margin-bottom: 15px;
        }

        .slider-nav {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .slider-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--light);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: var(--transition);
        }

        .slider-btn:hover {
            background: var(--secondary);
            color: white;
        }

        /* صفحة المنتجات */
        .products-page {
            padding: 80px 5%;
            background-color: #f5f7fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .product-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: var(--accent);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 10;
        }

        .view-details {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: var(--transition);
        }

        .product-card:hover .view-details {
            opacity: 1;
        }

        .view-details-btn {
            background: var(--secondary);
            color: white;
            padding: 10px 25px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* صفحة التواصل */
        .contact-page {
            padding: 80px 5%;
            background: linear-gradient(to right, var(--primary), var(--dark));
            color: white;
        }

        .contact-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .contact-info {
            padding: 30px;
        }

        .contact-info h3 {
            font-size: 2rem;
            margin-bottom: 30px;
            position: relative;
            padding-bottom: 15px;
        }

        .contact-info h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--secondary);
        }

        .contact-details {
            margin: 30px 0;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
        }

        .contact-icon {
            background: var(--secondary);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            font-size: 1.3rem;
            transition: var(--transition);
        }

        .social-links a:hover {
            background: var(--secondary);
            transform: translateY(-5px);
        }

        .contact-form {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: var(--shadow);
        }

        .contact-form h3 {
            color: var(--primary);
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--primary);
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
        }

        .form-group textarea {
            height: 150px;
            resize: vertical;
        }

        /* التذييل */
        .footer {
            background: var(--dark);
            color: white;
            padding: 50px 5% 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h3 {
            margin-bottom: 20px;
            color: var(--secondary);
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 10px;
        }

        .footer-section ul li a {
            color: #bdc3c7;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-section ul li a:hover {
            color: var(--secondary);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #bdc3c7;
        }

        /* النافذة المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 10px;
            width: 400px;
            max-width: 90%;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            left: 20px;
            cursor: pointer;
        }

        .close:hover {
            color: var(--accent);
        }

        .error-message {
            color: var(--accent);
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .no-results {
            text-align: center;
            padding: 50px 20px;
            color: var(--gray);
            font-size: 1.2rem;
            grid-column: 1 / -1;
        }

        /* لوحة التحكم */
        .dashboard {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            z-index: 1500;
            overflow-y: auto;
        }

        .admin-nav {
            background: var(--primary);
            color: white;
            padding: 20px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
        }

        .logout-btn {
            background: var(--accent);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .admin-container {
            padding: 30px 5%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2.5rem;
            color: var(--secondary);
            margin-bottom: 10px;
        }

        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .admin-card h3 {
            background: var(--primary);
            color: white;
            padding: 20px;
            margin: 0;
            font-size: 1.3rem;
        }

        .admin-card form {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px;
        }

        .admin-table th,
        .admin-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .admin-table th {
            background: var(--light);
            font-weight: 600;
            color: var(--primary);
        }

        .admin-table img {
            border-radius: 5px;
        }

        .action-btn {
            padding: 5px 10px;
            margin: 0 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .edit-btn {
            background: var(--secondary);
            color: white;
        }

        .delete-btn {
            background: var(--accent);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        /* معاينة الصورة */
        .image-preview {
            margin-top: 10px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background-color: #f9f9f9;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .image-preview.has-image {
            border-color: var(--secondary);
            background-color: white;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .image-preview span {
            color: var(--gray);
            font-style: italic;
        }

        .image-upload-info {
            margin-top: 5px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        /* نافذة تفاصيل المنتج */
        .product-modal {
            display: none;
            position: fixed;
            z-index: 3000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            overflow-y: auto;
        }

        .product-modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 1000px;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .product-modal-header {
            position: relative;
            padding: 20px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .product-modal-close {
            position: absolute;
            top: 15px;
            left: 20px;
            color: white;
            font-size: 30px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .product-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .product-modal-body {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .product-images {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .main-product-image {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .main-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .product-thumbnails {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding: 5px 0;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 3px solid transparent;
            transition: var(--transition);
            flex-shrink: 0;
        }

        .thumbnail.active {
            border-color: var(--secondary);
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .product-title {
            font-size: 2rem;
            color: var(--primary);
            margin: 0;
            font-weight: 700;
        }

        .product-category-badge {
            display: inline-block;
            background: var(--secondary);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            width: fit-content;
        }

        .product-price-large {
            font-size: 2.5rem;
            color: var(--accent);
            font-weight: 700;
            margin: 10px 0;
        }

        .product-description-full {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--gray);
            background: var(--light);
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid var(--secondary);
        }

        .product-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 1.2rem;
            border-radius: 8px;
            flex: 1;
        }

        .btn-primary {
            background: var(--secondary);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: var(--secondary);
            border: 2px solid var(--secondary);
        }

        .btn-secondary:hover {
            background: var(--secondary);
            color: white;
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hamburger {
                display: block;
            }

            .search-bar {
                width: 200px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .product-modal-body {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .main-product-image {
                height: 300px;
            }

            .product-title {
                font-size: 1.5rem;
            }

            .product-price-large {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTAgMjUwIj48cGF0aCBmaWxsPSIjMzQ5OERCQyIgZD0iTTEyNSAzMEwxNiAxNjJsMTkgNjBjMTEuNCAzMS45IDU5LjMgMzEuOSA3MC42IDBsMTkuOC02MC4xTDEyNSAzMHoiLz48cGF0aCBmaWxsPSIjMjQzQzYwIiBkPSJNMTI1IDMwTDIzMy44IDE2Mi4yIDEyNSAyMjUgMTYuMSAxNjIgMTI1IDMwTTEyNSAwTDAgOTcuMmw0Ny45IDE0NC4zTDEyNSAyNTBsNzcuMS04LjVMIDI1MCA5Ny4yIDEyNSAweiIvPjwvc3ZnPg==" alt="Logo">
            <h1>متجر التجارة الإلكترونية</h1>
        </div>
        <ul class="nav-links">
            <li><a href="#home">الصفحة الرئيسية</a></li>
            <li><a href="#products">المنتجات</a></li>
            <li><a href="#featured">منتجاتنا المميزة</a></li>
            <li><a href="#contact">تواصل معنا</a></li>
            <li><a href="#" id="adminLoginBtn" class="admin-btn">لوحة التحكم</a></li>
        </ul>
        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="ابحث عن منتج...">
            <button id="searchBtn"><i class="fas fa-search"></i></button>
        </div>
        <div class="hamburger">
            <i class="fas fa-bars"></i>
        </div>
    </nav>

    <!-- الصفحة الرئيسية -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>أفضل المنتجات بأفضل الأسعار</h1>
            <p>اكتشف تشكيلتنا الواسعة من المنتجات عالية الجودة التي تم اختيارها بعناية لتلبية جميع احتياجاتك. نقدم لكم أفضل العروض والخصومات الحصرية.</p>
            <a href="#products" class="btn">تصفح المنتجات</a>
        </div>
    </section>

    <!-- شريط المنتجات -->
    <section class="products-slider">
        <div class="section-title">
            <h2>أحدث المنتجات</h2>
        </div>
        <div class="slider-container">
            <div class="slider" id="productsSlider">
                <!-- سيتم إدراج المنتجات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- صفحة المنتجات -->
    <section id="products" class="products-page">
        <div class="section-title">
            <h2>جميع المنتجات</h2>
        </div>
        <div class="products-grid" id="productsGrid">
            <!-- سيتم إدراج المنتجات هنا ديناميكياً -->
        </div>
    </section>

    <!-- صفحة التواصل -->
    <section id="contact" class="contact-page">
        <div class="section-title">
            <h2>تواصل معنا</h2>
        </div>
        <div class="contact-container">
            <div class="contact-info">
                <h3>معلومات التواصل</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <h4>الهاتف</h4>
                            <p>+966 50 123 4567</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h4>البريد الإلكتروني</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h4>العنوان</h4>
                            <p>الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                </div>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
            <div class="contact-form">
                <h3>أرسل لنا رسالة</h3>
                <form>
                    <div class="form-group">
                        <label for="name">الاسم</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="message">الرسالة</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>
                    <button type="submit" class="btn">إرسال الرسالة</button>
                </form>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>متجر التجارة الإلكترونية</h3>
                <p>متجرك الإلكتروني الأول للحصول على أفضل المنتجات بأفضل الأسعار. نحن نقدم تشكيلة واسعة من المنتجات عالية الجودة.</p>
            </div>
            <div class="footer-section">
                <h3>روابط سريعة</h3>
                <ul>
                    <li><a href="#home">الصفحة الرئيسية</a></li>
                    <li><a href="#products">المنتجات</a></li>
                    <li><a href="#contact">تواصل معنا</a></li>
                    <li><a href="#">سياسة الخصوصية</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>خدمة العملاء</h3>
                <ul>
                    <li><a href="#">الأسئلة الشائعة</a></li>
                    <li><a href="#">سياسة الإرجاع</a></li>
                    <li><a href="#">الشحن والتوصيل</a></li>
                    <li><a href="#">الدعم الفني</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>جميع الحقوق محفوظة &copy; 2023 متجر التجارة الإلكترونية</p>
        </div>
    </footer>

    <!-- نافذة تسجيل الدخول للمدير -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تسجيل دخول المدير</h2>
            <form id="adminLoginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn">تسجيل الدخول</button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- لوحة التحكم الإدارية -->
    <div id="dashboard" class="dashboard">
        <div class="admin-nav">
            <h2>لوحة تحكم المدير</h2>
            <button class="logout-btn" id="logout-btn">تسجيل الخروج</button>
        </div>
        <div class="admin-container">
            <div class="admin-stats">
                <div class="stat-card">
                    <h3 id="totalProducts">0</h3>
                    <p>إجمالي المنتجات</p>
                </div>
                <div class="stat-card">
                    <h3>1,250</h3>
                    <p>إجمالي المبيعات</p>
                </div>
                <div class="stat-card">
                    <h3>84</h3>
                    <p>الطلبات الجديدة</p>
                </div>
            </div>

            <div class="admin-card">
                <h3>إضافة منتج جديد</h3>
                <form id="product-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-name">اسم المنتج</label>
                            <input type="text" id="product-name" required>
                        </div>
                        <div class="form-group">
                            <label for="product-price">السعر (ر.س)</label>
                            <input type="number" id="product-price" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-category">الفئة</label>
                            <select id="product-category" required>
                                <option value="">اختر فئة</option>
                                <option value="electronics">الكترونيات</option>
                                <option value="computers">أجهزة كمبيوتر</option>
                                <option value="phones">هواتف ذكية</option>
                                <option value="accessories">ملحقات</option>
                                <option value="home">أجهزة منزلية</option>
                                <option value="games">ألعاب</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="product-image">صورة المنتج</label>
                            <input type="file" id="product-image" accept="image/*" required>
                            <div class="image-upload-info">يُفضل استخدام صور بحجم 300x300 بكسل أو أكبر (JPG, PNG, GIF)</div>
                            <div id="imagePreview" class="image-preview">
                                <span>معاينة الصورة ستظهر هنا</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="product-description">وصف المنتج</label>
                        <textarea id="product-description" required></textarea>
                    </div>
                    <button type="submit" class="btn">إضافة المنتج</button>
                </form>
            </div>

            <div class="admin-card">
                <h3>قائمة المنتجات</h3>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>السعر</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="adminProductsTable">
                        <!-- سيتم إدراج المنتجات هنا ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل المنتج -->
    <div id="productModal" class="product-modal">
        <div class="product-modal-content">
            <div class="product-modal-header">
                <span class="product-modal-close">&times;</span>
                <h2>تفاصيل المنتج</h2>
            </div>
            <div class="product-modal-body">
                <div class="product-images">
                    <div class="main-product-image">
                        <img id="mainProductImage" src="" alt="صورة المنتج">
                    </div>
                    <div class="product-thumbnails" id="productThumbnails">
                        <!-- سيتم إدراج الصور المصغرة هنا -->
                    </div>
                </div>
                <div class="product-details">
                    <h1 class="product-title" id="modalProductTitle">اسم المنتج</h1>
                    <div class="product-category-badge" id="modalProductCategory">الفئة</div>
                    <div class="product-price-large" id="modalProductPrice">0 ر.س</div>
                    <div class="product-description-full" id="modalProductDescription">
                        وصف المنتج سيظهر هنا...
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-large btn-primary">
                            <i class="fas fa-shopping-cart"></i>
                            أضف إلى السلة
                        </button>
                        <button class="btn btn-large btn-secondary">
                            <i class="fas fa-heart"></i>
                            إضافة للمفضلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // إدارة المنتجات والبيانات
        let products = JSON.parse(localStorage.getItem('storeProducts')) || [];

        // بيانات الدخول الافتراضية
        const adminCredentials = {
            username: "admin",
            password: "admin123"
        };

        // عناصر DOM
        const loginModal = document.getElementById('loginModal');
        const adminLoginBtn = document.getElementById('adminLoginBtn');
        const adminLoginForm = document.getElementById('adminLoginForm');
        const dashboard = document.getElementById('dashboard');
        const closeBtn = document.querySelector('.close');
        const loginError = document.getElementById('loginError');

        // إعداد الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadProductsToPage();
            setupAdminEvents();
            checkAdminSession();
            setupSearchFunction();
            setupSlider();
            setupSmoothScrolling();
            setupImageUpload();
            setupProductModal();
        });

        // إعداد أحداث لوحة التحكم
        function setupAdminEvents() {
            // فتح نافذة تسجيل الدخول
            adminLoginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                loginModal.style.display = 'block';
            });

            // إغلاق النافذة
            closeBtn.addEventListener('click', () => {
                loginModal.style.display = 'none';
                loginError.textContent = '';
            });

            // إغلاق النافذة عند النقر خارجها
            window.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    loginModal.style.display = 'none';
                    loginError.textContent = '';
                }
            });

            // معالج تسجيل الدخول
            adminLoginForm.addEventListener('submit', handleAdminLogin);

            // زر تسجيل الخروج
            document.getElementById('logout-btn').addEventListener('click', handleLogout);

            // معالج إضافة منتج
            const productForm = document.getElementById('product-form');
            if (productForm) {
                productForm.addEventListener('submit', handleAddProduct);
            }
        }

        // معالج تسجيل دخول المدير
        function handleAdminLogin(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === adminCredentials.username && password === adminCredentials.password) {
                localStorage.setItem('adminLoggedIn', 'true');
                loginModal.style.display = 'none';
                dashboard.style.display = 'block';
                document.body.style.overflow = 'hidden';
                loginError.textContent = '';
                loadAdminProducts();
                updateProductCount();
            } else {
                loginError.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        }

        // معالج تسجيل الخروج
        function handleLogout() {
            localStorage.removeItem('adminLoggedIn');
            dashboard.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // فحص جلسة المدير
        function checkAdminSession() {
            if (localStorage.getItem('adminLoggedIn') === 'true') {
                dashboard.style.display = 'block';
                document.body.style.overflow = 'hidden';
                loadAdminProducts();
                updateProductCount();
            }
        }

        // معالج إضافة منتج جديد
        async function handleAddProduct(e) {
            e.preventDefault();

            try {
                // الحصول على بيانات الصورة
                const imageData = await getImageData();

                const newProduct = {
                    id: Date.now().toString(),
                    name: document.getElementById('product-name').value,
                    price: parseFloat(document.getElementById('product-price').value),
                    category: document.getElementById('product-category').value,
                    image: imageData, // استخدام بيانات الصورة المرفوعة
                    description: document.getElementById('product-description').value,
                    featured: Math.random() > 0.7 // عشوائي لجعل بعض المنتجات مميزة
                };

                // التحقق من صحة البيانات
                if (!newProduct.name || !newProduct.price || !newProduct.category || !newProduct.description) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // إضافة المنتج إلى القائمة
                products.push(newProduct);
                localStorage.setItem('storeProducts', JSON.stringify(products));

                // تحديث العرض
                loadProductsToPage();
                loadAdminProducts();
                updateProductCount();

                // مسح النموذج
                e.target.reset();

                // إعادة تعيين معاينة الصورة
                const imagePreview = document.getElementById('imagePreview');
                imagePreview.innerHTML = '<span>معاينة الصورة ستظهر هنا</span>';
                imagePreview.classList.remove('has-image');

                alert('تم إضافة المنتج بنجاح!');

            } catch (error) {
                alert('خطأ: ' + error);
            }
        }

        // تحميل المنتجات إلى الصفحة الرئيسية
        function loadProductsToPage() {
            const productsGrid = document.getElementById('productsGrid');
            if (!productsGrid) return;

            // مسح المنتجات الموجودة
            productsGrid.innerHTML = '';

            if (products.length === 0) {
                productsGrid.innerHTML = '<div class="no-results">لا توجد منتجات متاحة حالياً</div>';
                return;
            }

            // إضافة المنتجات الجديدة
            products.forEach(product => {
                const productCard = createProductCard(product);
                productsGrid.appendChild(productCard);
            });

            // تحديث شريط المنتجات أيضاً
            updateProductsSlider();
        }

        // إنشاء بطاقة منتج
        function createProductCard(product) {
            const card = document.createElement('div');
            card.className = 'product-card';
            card.style.cursor = 'pointer';

            card.innerHTML = `
                ${product.featured ? '<div class="featured-badge">مميز</div>' : ''}
                <div class="product-img">
                    <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة+غير+متوفرة'">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>${product.name}</h3>
                    <div class="product-price">${product.price} ر.س</div>
                </div>
            `;

            // إضافة حدث النقر لفتح تفاصيل المنتج
            card.addEventListener('click', () => {
                openProductModal(product);
            });

            return card;
        }

        // تحديث شريط المنتجات
        function updateProductsSlider() {
            const slider = document.getElementById('productsSlider');
            if (!slider) return;

            // مسح المحتوى الحالي
            slider.innerHTML = '';

            if (products.length === 0) {
                slider.innerHTML = '<div class="slide"><div class="product-info"><h3>لا توجد منتجات متاحة</h3><p>قم بإضافة منتجات من لوحة التحكم</p></div></div>';
                return;
            }

            // أول 5 منتجات للعرض في الشريط
            const sliderProducts = products.slice(0, 5);

            sliderProducts.forEach(product => {
                const slide = document.createElement('div');
                slide.className = 'slide';

                slide.innerHTML = `
                    <div class="product-img">
                        <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x250?text=صورة+غير+متوفرة'">
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="product-price">${product.price} ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                `;

                // إضافة حدث النقر لفتح تفاصيل المنتج
                slide.addEventListener('click', () => {
                    openProductModal(product);
                });

                slider.appendChild(slide);
            });
        }

        // تحميل المنتجات في لوحة التحكم
        function loadAdminProducts() {
            const tableBody = document.getElementById('adminProductsTable');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            if (products.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px;">لا توجد منتجات مضافة</td></tr>';
                return;
            }

            products.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><img src="${product.image}" alt="${product.name}" style="width:50px;height:50px;object-fit:cover;" onerror="this.src='https://via.placeholder.com/50x50?text=صورة'"></td>
                    <td>${product.name}</td>
                    <td>${getCategoryName(product.category)}</td>
                    <td>${product.price} ر.س</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editProduct('${product.id}')">تعديل</button>
                        <button class="action-btn delete-btn" onclick="deleteProduct('${product.id}')">حذف</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // الحصول على اسم الفئة بالعربية
        function getCategoryName(category) {
            const categories = {
                'electronics': 'الكترونيات',
                'computers': 'أجهزة كمبيوتر',
                'phones': 'هواتف ذكية',
                'accessories': 'ملحقات',
                'home': 'أجهزة منزلية',
                'games': 'ألعاب'
            };
            return categories[category] || category;
        }

        // حذف منتج
        function deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                products = products.filter(p => p.id !== productId);
                localStorage.setItem('storeProducts', JSON.stringify(products));
                loadProductsToPage();
                loadAdminProducts();
                updateProductCount();
            }
        }

        // تعديل منتج
        function editProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (product) {
                // ملء النموذج بالبيانات الحالية
                document.getElementById('product-name').value = product.name;
                document.getElementById('product-price').value = product.price;
                document.getElementById('product-category').value = product.category;
                document.getElementById('product-description').value = product.description;

                // عرض الصورة الحالية في المعاينة
                const imagePreview = document.getElementById('imagePreview');
                if (product.image) {
                    imagePreview.innerHTML = `<img src="${product.image}" alt="صورة المنتج الحالية">`;
                    imagePreview.classList.add('has-image');
                }

                // حذف المنتج القديم عند الحفظ
                deleteProduct(productId);

                // التمرير إلى نموذج الإضافة
                document.getElementById('product-form').scrollIntoView({ behavior: 'smooth' });
            }
        }

        // تحديث عدد المنتجات
        function updateProductCount() {
            const totalProductsEl = document.getElementById('totalProducts');
            if (totalProductsEl) {
                totalProductsEl.textContent = products.length;
            }
        }

        // إعداد وظيفة البحث
        function setupSearchFunction() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            if (searchInput && searchBtn) {
                // البحث عند النقر على الزر
                searchBtn.addEventListener('click', performSearch);

                // البحث عند الضغط على Enter
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });

                // البحث المباشر أثناء الكتابة
                searchInput.addEventListener('input', performSearch);
            }
        }

        // تنفيذ البحث
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const productsGrid = document.getElementById('productsGrid');

            if (!productsGrid) return;

            // مسح المنتجات الحالية
            productsGrid.innerHTML = '';

            // تصفية المنتجات حسب البحث
            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                getCategoryName(product.category).toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );

            // عرض النتائج
            if (filteredProducts.length === 0 && searchTerm !== '') {
                productsGrid.innerHTML = '<div class="no-results">لم يتم العثور على منتجات مطابقة للبحث</div>';
            } else {
                const productsToShow = searchTerm === '' ? products : filteredProducts;
                if (productsToShow.length === 0) {
                    productsGrid.innerHTML = '<div class="no-results">لا توجد منتجات متاحة حالياً</div>';
                } else {
                    productsToShow.forEach(product => {
                        const productCard = createProductCard(product);
                        productsGrid.appendChild(productCard);
                    });
                }
            }
        }

        // إعداد شريط التمرير
        function setupSlider() {
            const sliderContainer = document.querySelector('.slider-container');
            const slider = document.getElementById('productsSlider');

            if (!sliderContainer || !slider) return;

            // إضافة دعم التمرير بالماوس
            let isDown = false;
            let startX;
            let scrollLeft;

            sliderContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                sliderContainer.style.cursor = 'grabbing';
                startX = e.pageX - sliderContainer.offsetLeft;
                scrollLeft = sliderContainer.scrollLeft;
            });

            sliderContainer.addEventListener('mouseleave', () => {
                isDown = false;
                sliderContainer.style.cursor = 'grab';
            });

            sliderContainer.addEventListener('mouseup', () => {
                isDown = false;
                sliderContainer.style.cursor = 'grab';
            });

            sliderContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - sliderContainer.offsetLeft;
                const walk = (x - startX) * 2;
                sliderContainer.scrollLeft = scrollLeft - walk;
            });

            // إضافة دعم التمرير بالعجلة
            sliderContainer.addEventListener('wheel', (e) => {
                e.preventDefault();
                sliderContainer.scrollLeft += e.deltaY;
            });

            // تعيين مؤشر اليد
            sliderContainer.style.cursor = 'grab';
        }

        // إعداد التنقل السلس
        function setupSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // إعداد رفع الصور
        function setupImageUpload() {
            const imageInput = document.getElementById('product-image');
            const imagePreview = document.getElementById('imagePreview');

            if (imageInput && imagePreview) {
                imageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];

                    if (file) {
                        // التحقق من نوع الملف
                        if (!file.type.startsWith('image/')) {
                            alert('يرجى اختيار ملف صورة صالح (JPG, PNG, GIF)');
                            imageInput.value = '';
                            return;
                        }

                        // التحقق من حجم الملف (أقل من 5 ميجابايت)
                        if (file.size > 5 * 1024 * 1024) {
                            alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
                            imageInput.value = '';
                            return;
                        }

                        // قراءة الملف وعرض المعاينة
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            imagePreview.innerHTML = `<img src="${e.target.result}" alt="معاينة الصورة">`;
                            imagePreview.classList.add('has-image');
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // إعادة تعيين المعاينة
                        imagePreview.innerHTML = '<span>معاينة الصورة ستظهر هنا</span>';
                        imagePreview.classList.remove('has-image');
                    }
                });
            }
        }

        // الحصول على بيانات الصورة كـ Base64
        function getImageData() {
            return new Promise((resolve, reject) => {
                const imageInput = document.getElementById('product-image');
                const file = imageInput.files[0];
                const imagePreview = document.getElementById('imagePreview');

                if (!file) {
                    // التحقق من وجود صورة في المعاينة (في حالة التعديل)
                    const existingImage = imagePreview.querySelector('img');
                    if (existingImage && existingImage.src) {
                        resolve(existingImage.src);
                        return;
                    }
                    reject('لم يتم اختيار صورة');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    resolve(e.target.result);
                };
                reader.onerror = function() {
                    reject('خطأ في قراءة الصورة');
                };
                reader.readAsDataURL(file);
            });
        }

        // إعداد النافذة المنبثقة للمنتج
        function setupProductModal() {
            const modal = document.getElementById('productModal');
            const closeBtn = document.querySelector('.product-modal-close');

            // إغلاق النافذة عند النقر على زر الإغلاق
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            });

            // إغلاق النافذة عند النقر خارجها
            window.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });

            // إغلاق النافذة بالضغط على Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && modal.style.display === 'block') {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // فتح نافذة تفاصيل المنتج
        function openProductModal(product) {
            const modal = document.getElementById('productModal');
            const mainImage = document.getElementById('mainProductImage');
            const thumbnails = document.getElementById('productThumbnails');
            const title = document.getElementById('modalProductTitle');
            const category = document.getElementById('modalProductCategory');
            const price = document.getElementById('modalProductPrice');
            const description = document.getElementById('modalProductDescription');

            // تعبئة بيانات المنتج
            title.textContent = product.name;
            category.textContent = getCategoryName(product.category);
            price.textContent = `${product.price} ر.س`;
            description.textContent = product.description;

            // تعيين الصورة الرئيسية
            mainImage.src = product.image;
            mainImage.onerror = function() {
                this.src = 'https://via.placeholder.com/400x400?text=صورة+غير+متوفرة';
            };

            // إنشاء صور مصغرة (نفس الصورة مع إمكانية إضافة صور متعددة لاحقاً)
            thumbnails.innerHTML = '';
            const productImages = [product.image]; // يمكن إضافة صور متعددة هنا

            productImages.forEach((imageSrc, index) => {
                const thumbnail = document.createElement('div');
                thumbnail.className = `thumbnail ${index === 0 ? 'active' : ''}`;
                thumbnail.innerHTML = `<img src="${imageSrc}" alt="صورة ${index + 1}" onerror="this.src='https://via.placeholder.com/80x80?text=صورة'">`;

                thumbnail.addEventListener('click', () => {
                    // إزالة الفئة النشطة من جميع الصور المصغرة
                    thumbnails.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
                    // إضافة الفئة النشطة للصورة المحددة
                    thumbnail.classList.add('active');
                    // تغيير الصورة الرئيسية
                    mainImage.src = imageSrc;
                });

                thumbnails.appendChild(thumbnail);
            });

            // عرض النافذة
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // معلومات الدخول للمدير:
        // اسم المستخدم: admin
        // كلمة المرور: admin123
    </script>
</body>
</html>
