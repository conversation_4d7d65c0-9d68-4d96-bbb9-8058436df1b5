<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alfa Smile - عيادة الأسنان المتخصصة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* الشريط العلوي */
        .navbar {
            background-color: white;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-img {
            height: 50px;
            width: auto;
            object-fit: contain;
        }

        .logo h1 {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 700;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: #00d4ff;
        }

        .admin-btn {
            background: var(--accent) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 25px !important;
            transition: var(--transition) !important;
        }

        .admin-btn:hover {
            background: #c0392b !important;
            transform: translateY(-2px) !important;
        }

        /* البحث */
        .search-container {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 25px;
            padding: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .search-container:focus-within {
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        .search-input {
            border: none;
            outline: none;
            padding: 10px 15px;
            font-size: 1rem;
            background: transparent;
            width: 250px;
            font-family: 'Cairo', sans-serif;
        }

        .search-input::placeholder {
            color: var(--gray);
        }

        .search-btn {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: white;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-btn:hover {
            background: linear-gradient(135deg, #0099cc, #007399);
            transform: scale(1.1);
        }

        /* الصفحة الرئيسية */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            color: white;
            text-align: center;
            margin-top: 80px;
            overflow: hidden;
        }

        .hero-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(44, 62, 80, 0.7), rgba(52, 152, 219, 0.8));
            z-index: -1;
        }

        .hero-content {
            max-width: 800px;
            padding: 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 900;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .btn {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0, 212, 255, 0.4);
        }

        /* شريط المنتجات */
        .products-slider {
            padding: 80px 5%;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--primary);
            display: inline-block;
            padding-bottom: 15px;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--secondary);
        }

        .slider-container {
            position: relative;
            overflow: hidden;
            padding: 20px 0;
            margin: 0 60px;
        }

        .slider-wrapper {
            position: relative;
            overflow-x: auto;
            overflow-y: hidden;
            scrollbar-width: thin;
            scrollbar-color: #00d4ff var(--light);
            scroll-behavior: smooth;
        }

        .slider-wrapper::-webkit-scrollbar {
            height: 8px;
        }

        .slider-wrapper::-webkit-scrollbar-track {
            background: var(--light);
            border-radius: 4px;
        }

        .slider-wrapper::-webkit-scrollbar-thumb {
            background: #00d4ff;
            border-radius: 4px;
        }

        .slider-wrapper::-webkit-scrollbar-thumb:hover {
            background: #0099cc;
        }

        .slider {
            display: flex;
            gap: 20px;
            width: max-content;
        }

        .slide {
            min-width: 320px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            border: 2px solid transparent;
        }

        .slide:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
        }

        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
            opacity: 0;
            transition: var(--transition);
        }

        .slide:hover::before {
            opacity: 1;
        }

        .product-img {
            height: 280px;
            overflow: hidden;
            position: relative;
        }

        .product-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .slide:hover .product-img img {
            transform: scale(1.08);
        }

        .product-info {
            padding: 25px;
            position: relative;
        }

        .product-info h3 {
            font-size: 1.5rem;
            margin-bottom: 12px;
            color: var(--primary);
            font-weight: 600;
            line-height: 1.3;
        }

        .product-info p {
            color: var(--gray);
            margin-bottom: 18px;
            height: 65px;
            overflow: hidden;
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .product-price {
            font-size: 1.8rem;
            color: #00d4ff;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 212, 255, 0.3);
        }

        .product-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
        }

        /* قسم الصور الاحترافية */
        .professional-section {
            padding: 80px 5%;
            background: #f8f9fa;
        }

        .image-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .showcase-item {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .showcase-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 212, 255, 0.15);
        }

        .showcase-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.8), rgba(0, 153, 204, 0.9));
            opacity: 0;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .showcase-item:hover::after {
            opacity: 1;
        }

        .showcase-item::before {
            content: 'انقر للمزيد';
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            z-index: 10;
            opacity: 0;
            transition: var(--transition);
            background: rgba(0, 212, 255, 0.9);
            padding: 8px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .showcase-item:hover::before {
            opacity: 1;
            transform: translateX(-50%) translateY(-5px);
        }

        .showcase-image {
            height: 300px;
            overflow: hidden;
            position: relative;
            width: 100%;
        }

        .showcase-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .showcase-item:hover .showcase-image img {
            transform: scale(1.1);
        }

        /* صفحة المنتجات */
        .products-page {
            padding: 80px 5%;
            background-color: #f5f7fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            position: relative;
            border: 2px solid transparent;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.15);
            border-color: #00d4ff;
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            z-index: 10;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        /* صفحة التواصل */
        .contact-page {
            padding: 80px 5%;
            background: linear-gradient(to right, var(--primary), var(--dark));
            color: white;
        }

        .contact-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .contact-info {
            padding: 30px;
        }

        .contact-info h3 {
            font-size: 2rem;
            margin-bottom: 30px;
            position: relative;
            padding-bottom: 15px;
        }

        .contact-info h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--secondary);
        }

        .contact-details {
            margin: 30px 0;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
        }

        .contact-icon {
            background: var(--secondary);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            font-size: 1.3rem;
            transition: var(--transition);
        }

        .social-links a:hover {
            background: var(--secondary);
            transform: translateY(-5px);
        }

        /* النافذة المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            left: 20px;
            cursor: pointer;
            z-index: 10;
            background: rgba(255, 255, 255, 0.9);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close:hover {
            color: var(--accent);
        }

        .modal-body {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
        }

        .modal-image {
            position: relative;
        }

        .modal-image img {
            width: 100%;
            height: 400px;
            object-fit: cover;
        }

        .thumbnails {
            display: flex;
            gap: 10px;
            padding: 15px;
            overflow-x: auto;
        }

        .thumbnail {
            min-width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: var(--transition);
        }

        .thumbnail.active {
            border-color: #00d4ff;
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .modal-details {
            padding: 30px;
        }

        .modal-details h2 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .modal-price {
            font-size: 2rem;
            color: #00d4ff;
            font-weight: 700;
            margin: 15px 0;
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .slider-container {
                margin: 0 20px;
            }

            .modal-body {
                grid-template-columns: 1fr;
            }

            .modal-image img {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="logo">
            <img src="images/WhatsApp_Image_2025-01-22_at_9.46.14_PM-removebg-preview.webp" alt="Alfa Smile Logo" class="logo-img">
            <h1>Alfa Smile</h1>
        </div>
        <ul class="nav-links">
            <li><a href="#home">الصفحة الرئيسية</a></li>
            <li><a href="#products">المنتجات</a></li>
            <li><a href="#contact">تواصل معنا</a></li>
            <li><a href="#" id="adminLoginBtn" class="admin-btn">لوحة التحكم</a></li>
        </ul>
        <div class="search-container">
            <input type="text" id="searchInput" placeholder="ابحث عن منتج..." class="search-input">
            <button class="search-btn" onclick="searchProducts()">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </nav>

    <!-- الصفحة الرئيسية -->
    <section id="home" class="hero">
        <video class="hero-video" autoplay muted loop>
            <source src="video/Alfa Smile Dental Clinic.mp4" type="video/mp4">
        </video>
        <div class="hero-overlay"></div>
        <div class="hero-content">
            <h1>مرحباً بكم في Alfa Smile</h1>
            <p>عيادة الأسنان المتخصصة - نقدم لكم أفضل الخدمات والمنتجات لصحة أسنانكم</p>
            <a href="#products" class="btn">تصفح منتجاتنا</a>
        </div>
    </section>

    <!-- شريط المنتجات -->
    <section class="products-slider">
        <div class="section-title">
            <h2>أحدث المنتجات</h2>
        </div>
        <div class="slider-container">
            <div class="slider-wrapper" id="sliderWrapper">
                <div class="slider" id="productsSlider">
                    <!-- سيتم إدراج المنتجات هنا ديناميكياً -->
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الصور الاحترافية -->
    <section class="professional-section">
        <div class="section-title">
            <h2>منتجاتنا المتخصصة</h2>
        </div>
        <div class="image-showcase">
            <div class="showcase-item" onclick="openProductPage('whitening')">
                <div class="showcase-image">
                    <img src="images/WhatsApp_Image_2025-01-22_at_10.38.07_PM_3.webp" alt="تبييض الأسنان المتقدم">
                </div>
            </div>

            <div class="showcase-item" onclick="openProductPage('corrector')">
                <div class="showcase-image">
                    <img src="images/pc1_b3980133-48c9-4ada-9f22-12e388593ffb_1600x.webp" alt="مصحح الأسنان البنفسجي">
                </div>
            </div>
        </div>
    </section>

    <!-- قسم جميع المنتجات -->
    <section id="products" class="products-page">
        <div class="section-title">
            <h2>جميع منتجاتنا</h2>
        </div>
        <div class="products-grid" id="productsGrid">
            <!-- سيتم إدراج المنتجات هنا ديناميكياً -->
        </div>
    </section>

    <!-- قسم التواصل -->
    <section id="contact" class="contact-page">
        <div class="contact-container">
            <div class="contact-info">
                <h3>تواصل معنا</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <h4>الهاتف</h4>
                            <p>+966 50 123 4567</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h4>البريد الإلكتروني</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h4>العنوان</h4>
                            <p>الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                </div>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-whatsapp"></i></a>
                </div>
            </div>
        </div>
    </section>

    <!-- النافذة المنبثقة للمنتجات -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeProductModal()">&times;</span>
            <div class="modal-body">
                <div class="modal-image">
                    <img id="modalMainImage" src="" alt="">
                    <div id="modalThumbnails" class="thumbnails"></div>
                </div>
                <div class="modal-details">
                    <h2 id="modalProductTitle"></h2>
                    <p id="modalProductCategory"></p>
                    <div id="modalProductPrice" class="modal-price"></div>
                    <p id="modalProductDescription"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المنتجات
        let products = JSON.parse(localStorage.getItem('storeProducts')) || [];

        // تحديث شريط المنتجات
        function updateProductsSlider() {
            const slider = document.getElementById('productsSlider');
            if (!slider) return;

            slider.innerHTML = '';

            if (products.length === 0) {
                slider.innerHTML = '<div class="slide"><div class="product-info"><h3>لا توجد منتجات متاحة</h3><p>قم بإضافة منتجات من لوحة التحكم</p></div></div>';
                return;
            }

            products.forEach(product => {
                const slide = document.createElement('div');
                slide.className = 'slide';

                const imageUrl = Array.isArray(product.image) ? product.image[0] : product.image;

                slide.innerHTML = `
                    <div class="product-img">
                        ${product.featured ? '<div class="product-badge">مميز</div>' : ''}
                        <img src="${imageUrl}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x250?text=صورة+غير+متوفرة'">
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="product-price">${product.price} ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                `;

                slide.addEventListener('click', () => {
                    openProductModal(product);
                });

                slider.appendChild(slide);
            });
        }

        // تحديث شبكة المنتجات
        function updateProductsGrid() {
            const grid = document.getElementById('productsGrid');
            if (!grid) return;

            grid.innerHTML = '';

            if (products.length === 0) {
                grid.innerHTML = '<div style="text-align: center; grid-column: 1/-1; padding: 50px;"><h3>لا توجد منتجات متاحة</h3><p>قم بإضافة منتجات من لوحة التحكم</p></div>';
                return;
            }

            products.forEach(product => {
                const card = document.createElement('div');
                card.className = 'product-card';

                const imageUrl = Array.isArray(product.image) ? product.image[0] : product.image;

                card.innerHTML = `
                    ${product.featured ? '<div class="featured-badge">مميز</div>' : ''}
                    <div class="product-img">
                        <img src="${imageUrl}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة+غير+متوفرة'">
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="product-price">${product.price} ر.س</div>
                        <button class="btn">عرض التفاصيل</button>
                    </div>
                `;

                card.addEventListener('click', () => {
                    openProductModal(product);
                });

                grid.appendChild(card);
            });
        }

        // فتح النافذة المنبثقة للمنتج
        function openProductModal(product) {
            const modal = document.getElementById('productModal');
            const modalTitle = document.getElementById('modalProductTitle');
            const modalCategory = document.getElementById('modalProductCategory');
            const modalPrice = document.getElementById('modalProductPrice');
            const modalDescription = document.getElementById('modalProductDescription');
            const mainImage = document.getElementById('modalMainImage');
            const thumbnails = document.getElementById('modalThumbnails');

            modalTitle.textContent = product.name;
            modalCategory.textContent = product.category || 'منتجات الأسنان';
            modalPrice.textContent = product.price + ' ر.س';
            modalDescription.textContent = product.description;

            // إعداد الصور
            const images = Array.isArray(product.image) ? product.image : [product.image];

            // الصورة الرئيسية
            mainImage.src = images[0];

            // الصور المصغرة
            thumbnails.innerHTML = '';
            if (images.length > 1) {
                images.forEach((imageSrc, index) => {
                    const thumbnail = document.createElement('div');
                    thumbnail.className = 'thumbnail' + (index === 0 ? ' active' : '');
                    thumbnail.innerHTML = `<img src="${imageSrc}" alt="صورة ${index + 1}">`;

                    thumbnail.addEventListener('click', () => {
                        mainImage.src = imageSrc;
                        document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
                        thumbnail.classList.add('active');
                    });

                    thumbnails.appendChild(thumbnail);
                });
            }

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // إغلاق النافذة المنبثقة
        function closeProductModal() {
            const modal = document.getElementById('productModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // فتح الصفحات المتخصصة
        function openProductPage(type) {
            if (type === 'whitening') {
                window.open('whitening.html', '_blank');
            } else if (type === 'corrector') {
                window.open('corrector.html', '_blank');
            }
        }

        // تحميل المنتجات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateProductsSlider();
            updateProductsGrid();
        });

        // وظيفة البحث
        function searchProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();

            if (!searchTerm) {
                updateProductsSlider();
                updateProductsGrid();
                return;
            }

            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                (product.category && product.category.toLowerCase().includes(searchTerm))
            );

            // تحديث الشريط
            const slider = document.getElementById('productsSlider');
            slider.innerHTML = '';

            if (filteredProducts.length === 0) {
                slider.innerHTML = '<div class="slide"><div class="product-info"><h3>لم يتم العثور على منتجات</h3><p>جرب البحث بكلمات أخرى</p></div></div>';
            } else {
                filteredProducts.forEach(product => {
                    const slide = document.createElement('div');
                    slide.className = 'slide';

                    const imageUrl = Array.isArray(product.image) ? product.image[0] : product.image;

                    slide.innerHTML = `
                        <div class="product-img">
                            ${product.featured ? '<div class="product-badge">مميز</div>' : ''}
                            <img src="${imageUrl}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x250?text=صورة+غير+متوفرة'">
                        </div>
                        <div class="product-info">
                            <h3>${product.name}</h3>
                            <p>${product.description}</p>
                            <div class="product-price">${product.price} ر.س</div>
                            <button class="btn">أضف إلى السلة</button>
                        </div>
                    `;

                    slide.addEventListener('click', () => {
                        openProductModal(product);
                    });

                    slider.appendChild(slide);
                });
            }

            // تحديث الشبكة
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = '';

            if (filteredProducts.length === 0) {
                grid.innerHTML = '<div style="text-align: center; grid-column: 1/-1; padding: 50px;"><h3>لم يتم العثور على منتجات</h3><p>جرب البحث بكلمات أخرى</p></div>';
            } else {
                filteredProducts.forEach(product => {
                    const card = document.createElement('div');
                    card.className = 'product-card';

                    const imageUrl = Array.isArray(product.image) ? product.image[0] : product.image;

                    card.innerHTML = `
                        ${product.featured ? '<div class="featured-badge">مميز</div>' : ''}
                        <div class="product-img">
                            <img src="${imageUrl}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة+غير+متوفرة'">
                        </div>
                        <div class="product-info">
                            <h3>${product.name}</h3>
                            <p>${product.description}</p>
                            <div class="product-price">${product.price} ر.س</div>
                            <button class="btn">عرض التفاصيل</button>
                        </div>
                    `;

                    card.addEventListener('click', () => {
                        openProductModal(product);
                    });

                    grid.appendChild(card);
                });
            }

            // التمرير إلى قسم المنتجات
            document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
        }

        // إضافة حدث البحث عند الضغط على Enter
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchProducts();
            }
        });

        // وظيفة لوحة التحكم
        document.getElementById('adminLoginBtn').addEventListener('click', function(e) {
            e.preventDefault();
            window.open('admin_panel.html', '_blank');
        });

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('productModal');
            if (event.target === modal) {
                closeProductModal();
            }
        });
    </script>
</body>
</html>
