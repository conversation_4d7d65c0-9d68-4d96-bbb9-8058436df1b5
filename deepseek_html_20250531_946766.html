<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة التجارة الإلكترونية - متجرك الإلكتروني الأول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --gray: #7f8c8d;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* الشريط العلوي */
        .navbar {
            background-color: white;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo img {
            height: 50px;
        }

        .logo h1 {
            font-size: 1.5rem;
            color: var(--primary);
            font-weight: 700;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: var(--secondary);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 3px;
            background-color: var(--secondary);
            transition: var(--transition);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .admin-btn {
            background: var(--accent) !important;
            color: white !important;
            padding: 8px 20px !important;
            border-radius: 20px !important;
            transition: var(--transition) !important;
        }

        .admin-btn:hover {
            background: #c0392b !important;
            transform: translateY(-2px) !important;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background-color: var(--light);
            border-radius: 30px;
            padding: 8px 15px;
            width: 300px;
        }

        .search-bar input {
            border: none;
            background: transparent;
            width: 100%;
            padding: 5px 10px;
            font-size: 1rem;
            outline: none;
        }

        .search-bar button {
            background: transparent;
            border: none;
            color: var(--secondary);
            cursor: pointer;
            font-size: 1.2rem;
        }

        .hamburger {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* الصفحة الرئيسية */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover no-repeat;
            color: white;
            text-align: center;
            margin-top: 80px;
        }

        .hero-content {
            max-width: 800px;
            padding: 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 900;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: var(--secondary);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
        }

        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* شريط المنتجات */
        .products-slider {
            padding: 80px 5%;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--primary);
            display: inline-block;
            padding-bottom: 15px;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--secondary);
        }

        .slider-container {
            position: relative;
            overflow: hidden;
            padding: 20px 0;
        }

        .slider {
            display: flex;
            transition: transform 0.5s ease;
            gap: 30px;
        }

        .slide {
            min-width: 300px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .slide:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .product-img {
            height: 250px;
            overflow: hidden;
        }

        .product-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .slide:hover .product-img img {
            transform: scale(1.1);
        }

        .product-info {
            padding: 20px;
        }

        .product-info h3 {
            font-size: 1.4rem;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .product-info p {
            color: var(--gray);
            margin-bottom: 15px;
            height: 60px;
            overflow: hidden;
        }

        .product-price {
            font-size: 1.5rem;
            color: var(--accent);
            font-weight: 700;
            margin-bottom: 15px;
        }

        .slider-nav {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .slider-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--light);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: var(--transition);
        }

        .slider-btn:hover {
            background: var(--secondary);
            color: white;
        }

        /* صفحة المنتجات */
        .products-page {
            padding: 80px 5%;
            background-color: #f5f7fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .product-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: var(--accent);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 10;
        }

        .view-details {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: var(--transition);
        }

        .product-card:hover .view-details {
            opacity: 1;
        }

        .view-details-btn {
            background: var(--secondary);
            color: white;
            padding: 10px 25px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* صفحة التواصل */
        .contact-page {
            padding: 80px 5%;
            background: linear-gradient(to right, var(--primary), var(--dark));
            color: white;
        }

        .contact-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .contact-info {
            padding: 30px;
        }

        .contact-info h3 {
            font-size: 2rem;
            margin-bottom: 30px;
            position: relative;
            padding-bottom: 15px;
        }

        .contact-info h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--secondary);
        }

        .contact-details {
            margin: 30px 0;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
        }

        .contact-icon {
            background: var(--secondary);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            font-size: 1.3rem;
            transition: var(--transition);
        }

        .social-links a:hover {
            background: var(--secondary);
            transform: translateY(-5px);
        }

        .contact-form {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: var(--shadow);
        }

        .contact-form h3 {
            color: var(--primary);
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--primary);
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
        }

        .form-group textarea {
            height: 150px;
            resize: vertical;
        }

        /* التذييل */
        .footer {
            background: var(--dark);
            color: white;
            padding: 50px 5% 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-column h3 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            position: relative;
            padding-bottom: 10px;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: var(--secondary);
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 12px;
        }

        .footer-links a {
            color: #bdc3c7;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--secondary);
            padding-right: 5px;
        }

        .copyright {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #bdc3c7;
        }

        /* النافذة المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 10px;
            width: 400px;
            max-width: 90%;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            left: 20px;
            cursor: pointer;
        }

        .close:hover {
            color: var(--accent);
        }

        .error-message {
            color: var(--accent);
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .no-results {
            text-align: center;
            padding: 50px 20px;
            color: var(--gray);
            font-size: 1.2rem;
            grid-column: 1 / -1;
        }

        /* لوحة التحكم */
        .dashboard {
            display: none;
            min-height: 100vh;
            background: #f5f7fa;
        }

        .admin-nav {
            background: var(--primary);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-nav h2 {
            font-size: 1.8rem;
        }

        .logout-btn {
            background: var(--accent);
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .admin-container {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 250px;
            background: white;
            box-shadow: var(--shadow);
            padding: 20px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 12px 25px;
            color: var(--dark);
            text-decoration: none;
            transition: var(--transition);
            font-weight: 600;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: var(--light);
            color: var(--secondary);
            border-right: 4px solid var(--secondary);
        }

        .admin-main {
            flex: 1;
            padding: 30px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title {
            font-size: 1.8rem;
            color: var(--primary);
        }

        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 25px;
            margin-bottom: 30px;
        }

        .admin-card h3 {
            margin-bottom: 20px;
            color: var(--primary);
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--dark);
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Cairo', sans-serif;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .admin-table th,
        .admin-table td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }

        .admin-table th {
            background: var(--light);
            font-weight: 700;
            color: var(--primary);
        }

        .admin-table tr:hover {
            background: #f9f9f9;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
            margin-left: 5px;
        }

        .edit-btn {
            background: var(--secondary);
            color: white;
        }

        .delete-btn {
            background: var(--accent);
            color: white;
        }

        .action-btn:hover {
            opacity: 0.9;
        }

        /* تعديلات للعرض الجوال */
        @media (max-width: 992px) {
            .nav-links {
                display: none;
            }

            .hamburger {
                display: block;
            }

            .search-bar {
                width: 200px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }

        @media (max-width: 768px) {
            .search-bar {
                display: none;
            }

            .logo h1 {
                font-size: 1.3rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .slide {
                min-width: 250px;
            }

            .footer-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTAgMjUwIj48cGF0aCBmaWxsPSIjMzQ5OERCQyIgZD0iTTEyNSAzMEwxNiAxNjJsMTkgNjBjMTEuNCAzMS45IDU5LjMgMzEuOSA3MC42IDBsMTkuOC02MC4xTDEyNSAzMHoiLz48cGF0aCBmaWxsPSIjMjQzQzYwIiBkPSJNMTI1IDMwTDIzMy44IDE2Mi4yIDEyNSAyMjUgMTYuMSAxNjIgMTI1IDMwTTEyNSAwTDAgOTcuMmw0Ny45IDE0NC4zTDEyNSAyNTBsNzcuMS04LjVMIDI1MCA5Ny4yIDEyNSAweiIvPjwvc3ZnPg==" alt="Logo">
            <h1>متجر التجارة الإلكترونية</h1>
        </div>
        <ul class="nav-links">
            <li><a href="#home">الصفحة الرئيسية</a></li>
            <li><a href="#products">المنتجات</a></li>
            <li><a href="#featured">منتجاتنا المميزة</a></li>
            <li><a href="#contact">تواصل معنا</a></li>
            <li><a href="#" id="adminLoginBtn" class="admin-btn">لوحة التحكم</a></li>
        </ul>
        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="ابحث عن منتج...">
            <button id="searchBtn"><i class="fas fa-search"></i></button>
        </div>
        <div class="hamburger">
            <i class="fas fa-bars"></i>
        </div>
    </nav>

    <!-- الصفحة الرئيسية -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>أفضل المنتجات بأفضل الأسعار</h1>
            <p>اكتشف تشكيلتنا الواسعة من المنتجات عالية الجودة التي تم اختيارها بعناية لتلبية جميع احتياجاتك. نقدم لكم أفضل العروض والخصومات الحصرية.</p>
            <a href="#products" class="btn">تصفح المنتجات</a>
        </div>
    </section>

    <!-- شريط المنتجات -->
    <section class="products-slider">
        <div class="section-title">
            <h2>أحدث المنتجات</h2>
        </div>
        <div class="slider-container">
            <div class="slider">
                <div class="slide">
                    <div class="product-img">
                        <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1999&q=80" alt="منتج 1">
                    </div>
                    <div class="product-info">
                        <h3>ساعة ذكية فاخرة</h3>
                        <p>ساعة ذكية متطورة مع شاشة لمس عالية الدقة ومقاومة للماء</p>
                        <div class="product-price">650 ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                </div>
                <div class="slide">
                    <div class="product-img">
                        <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="منتج 2">
                    </div>
                    <div class="product-info">
                        <h3>سماعات لاسلكية</h3>
                        <p>سماعات بلوتوث عالية الجودة مع عزل صوتي ممتاز وبطارية طويلة الأمد</p>
                        <div class="product-price">320 ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                </div>
                <div class="slide">
                    <div class="product-img">
                        <img src="https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1964&q=80" alt="منتج 3">
                    </div>
                    <div class="product-info">
                        <h3>هاتف ذكي حديث</h3>
                        <p>أحدث طراز من الهواتف الذكية بكاميرا احترافية وأداء فائق</p>
                        <div class="product-price">2,899 ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                </div>
                <div class="slide">
                    <div class="product-img">
                        <img src="https://images.unsplash.com/photo-1504274066651-8d31a536b11a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1935&q=80" alt="منتج 4">
                    </div>
                    <div class="product-info">
                        <h3>كاميرا احترافية</h3>
                        <p>كاميرا احترافية بدقة 24.2 ميجابكسل مع عدسات متعددة</p>
                        <div class="product-price">3,450 ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                </div>
                <div class="slide">
                    <div class="product-img">
                        <img src="https://images.unsplash.com/photo-1585155770447-2f66e2a397b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80" alt="منتج 5">
                    </div>
                    <div class="product-info">
                        <h3>جهاز لوحي متطور</h3>
                        <p>جهاز لوحي بشاشة 10 بوصة وبطارية تدوم طوال اليوم</p>
                        <div class="product-price">1,850 ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="slider-nav">
            <button class="slider-btn prev"><i class="fas fa-arrow-right"></i></button>
            <button class="slider-btn next"><i class="fas fa-arrow-left"></i></button>
        </div>
    </section>

    <!-- صفحة المنتجات -->
    <section id="products" class="products-page">
        <div class="section-title">
            <h2>جميع المنتجات</h2>
        </div>
        <div class="products-grid">
            <div class="product-card">
                <div class="featured-badge">مميز</div>
                <div class="product-img">
                    <img src="https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1889&q=80" alt="منتج">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>حقيبة كمبيوتر محمول</h3>
                    <div class="product-price">189 ر.س</div>
                </div>
            </div>
            <div class="product-card">
                <div class="product-img">
                    <img src="https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="منتج">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>كاميرا كانون</h3>
                    <div class="product-price">2,150 ر.س</div>
                </div>
            </div>
            <div class="product-card">
                <div class="featured-badge">مميز</div>
                <div class="product-img">
                    <img src="https://images.unsplash.com/photo-1593642632823-8f785ba67e45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80" alt="منتج">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>لابتوب ديل</h3>
                    <div class="product-price">4,299 ر.س</div>
                </div>
            </div>
            <div class="product-card">
                <div class="product-img">
                    <img src="https://images.unsplash.com/photo-1541807084-5c52b6b3adef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80" alt="منتج">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>لوحة مفاتيح ميكانيكية</h3>
                    <div class="product-price">249 ر.س</div>
                </div>
            </div>
            <div class="product-card">
                <div class="featured-badge">مميز</div>
                <div class="product-img">
                    <img src="https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1925&q=80" alt="منتج">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>سماعات ألعاب</h3>
                    <div class="product-price">189 ر.س</div>
                </div>
            </div>
            <div class="product-card">
                <div class="product-img">
                    <img src="https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80" alt="منتج">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>ماوس ألعاب</h3>
                    <div class="product-price">149 ر.س</div>
                </div>
            </div>
        </div>
    </section>

    <!-- صفحة التواصل -->
    <section id="contact" class="contact-page">
        <div class="section-title">
            <h2>تواصل معنا</h2>
        </div>
        <div class="contact-container">
            <div class="contact-info">
                <h3>معلومات التواصل</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <h4>الهاتف</h4>
                            <p>+966 11 123 4567</p>
                            <p>+966 50 123 4567</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h4>البريد الإلكتروني</h4>
                            <p><EMAIL></p>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h4>العنوان</h4>
                            <p>شارع الملك فهد، الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                </div>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#"><i class="fab fa-whatsapp"></i></a>
                </div>
            </div>
            <div class="contact-form">
                <h3>أرسل لنا رسالة</h3>
                <form>
                    <div class="form-group">
                        <label for="name">الاسم الكامل</label>
                        <input type="text" id="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone">
                    </div>
                    <div class="form-group">
                        <label for="message">الرسالة</label>
                        <textarea id="message" required></textarea>
                    </div>
                    <button type="submit" class="btn">إرسال الرسالة</button>
                </form>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-column">
                <h3>عن الشركة</h3>
                <p>نحن متجر إلكتروني رائد نقدم أفضل المنتجات بأفضل الأسعار. نسعى دائماً لتقديم تجربة تسوق مميزة لعملائنا الكرام.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
            <div class="footer-column">
                <h3>روابط سريعة</h3>
                <ul class="footer-links">
                    <li><a href="#home">الصفحة الرئيسية</a></li>
                    <li><a href="#products">المنتجات</a></li>
                    <li><a href="#featured">منتجات مميزة</a></li>
                    <li><a href="#contact">تواصل معنا</a></li>
                    <li><a href="#">سياسة الخصوصية</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>فئات المنتجات</h3>
                <ul class="footer-links">
                    <li><a href="#">الكترونيات</a></li>
                    <li><a href="#">أجهزة كمبيوتر</a></li>
                    <li><a href="#">هواتف ذكية</a></li>
                    <li><a href="#">ملحقات</a></li>
                    <li><a href">أجهزة منزلية</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>اشترك في النشرة البريدية</h3>
                <p>اشترك لتصلك أحدث العروض والتخفيضات</p>
                <div class="search-bar" style="width:100%; background:rgba(255,255,255,0.1);">
                    <input type="email" placeholder="بريدك الإلكتروني">
                    <button><i class="fas fa-paper-plane"></i></button>
                </div>
            </div>
        </div>
        <div class="copyright">
            <p>جميع الحقوق محفوظة &copy; 2023 متجر التجارة الإلكترونية</p>
        </div>
    </footer>

    <!-- نافذة تسجيل الدخول للمدير -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تسجيل دخول المدير</h2>
            <form id="adminLoginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn">تسجيل الدخول</button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- لوحة التحكم الإدارية -->
    <div id="dashboard" class="dashboard">
        <div class="admin-nav">
            <h2>لوحة تحكم المدير</h2>
            <button class="logout-btn" id="logout-btn">تسجيل الخروج</button>
        </div>
        <div class="admin-container">
            <div class="sidebar">
                <ul class="sidebar-menu">
                    <li><a href="#" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="#"><i class="fas fa-box"></i> إدارة المنتجات</a></li>
                    <li><a href="#"><i class="fas fa-tags"></i> الفئات</a></li>
                    <li><a href="#"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="#"><i class="fas fa-users"></i> العملاء</a></li>
                    <li><a href="#"><i class="fas fa-chart-line"></i> التقارير</a></li>
                    <li><a href="#"><i class="fas fa-cog"></i> الإعدادات</a></li>
                </ul>
            </div>
            <div class="admin-main">
                <div class="admin-header">
                    <h2 class="admin-title">إدارة المنتجات</h2>
                    <button class="btn" id="add-product-btn">إضافة منتج جديد</button>
                </div>
                
                <div class="admin-card">
                    <h3>إضافة منتج جديد</h3>
                    <form id="product-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-name">اسم المنتج</label>
                                <input type="text" id="product-name" required>
                            </div>
                            <div class="form-group">
                                <label for="product-price">السعر (ر.س)</label>
                                <input type="number" id="product-price" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-category">الفئة</label>
                                <select id="product-category" required>
                                    <option value="">اختر فئة</option>
                                    <option value="electronics">الكترونيات</option>
                                    <option value="computers">أجهزة كمبيوتر</option>
                                    <option value="phones">هواتف ذكية</option>
                                    <option value="accessories">ملحقات</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="product-image">صورة المنتج (رابط)</label>
                                <input type="url" id="product-image" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="product-description">وصف المنتج</label>
                            <textarea id="product-description" required></textarea>
                        </div>
                        <button type="submit" class="btn">حفظ المنتج</button>
                    </form>
                </div>
                
                <div class="admin-card">
                    <h3>قائمة المنتجات</h3>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1999&q=80" alt="منتج" style="width:50px;height:50px;object-fit:cover;"></td>
                                <td>ساعة ذكية فاخرة</td>
                                <td>الكترونيات</td>
                                <td>650 ر.س</td>
                                <td>
                                    <button class="action-btn edit-btn">تعديل</button>
                                    <button class="action-btn delete-btn">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="منتج" style="width:50px;height:50px;object-fit:cover;"></td>
                                <td>سماعات لاسلكية</td>
                                <td>ملحقات</td>
                                <td>320 ر.س</td>
                                <td>
                                    <button class="action-btn edit-btn">تعديل</button>
                                    <button class="action-btn delete-btn">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1964&q=80" alt="منتج" style="width:50px;height:50px;object-fit:cover;"></td>
                                <td>هاتف ذكي حديث</td>
                                <td>هواتف ذكية</td>
                                <td>2,899 ر.س</td>
                                <td>
                                    <button class="action-btn edit-btn">تعديل</button>
                                    <button class="action-btn delete-btn">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // التحكم في شريط المنتجات المنزلقة
        const slider = document.querySelector('.slider');
        const slides = document.querySelectorAll('.slide');
        const prevBtn = document.querySelector('.prev');
        const nextBtn = document.querySelector('.next');
        
        let currentIndex = 0;
        const slideWidth = slides[0].offsetWidth + 30; // العرض + الهوامش
        
        function goToSlide(index) {
            if (index < 0) index = slides.length - 1;
            if (index >= slides.length) index = 0;
            
            currentIndex = index;
            slider.style.transform = `translateX(${-currentIndex * slideWidth}px)`;
        }
        
        prevBtn.addEventListener('click', () => goToSlide(currentIndex - 1));
        nextBtn.addEventListener('click', () => goToSlide(currentIndex + 1));
        
        // تلقائي التمرير كل 5 ثواني
        setInterval(() => {
            goToSlide(currentIndex + 1);
        }, 5000);
        
        // التنقل السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // إدارة المنتجات والبيانات
        let products = JSON.parse(localStorage.getItem('storeProducts')) || [];

        // بيانات الدخول الافتراضية
        const adminCredentials = {
            username: "admin",
            password: "admin123"
        };

        // عناصر DOM
        const loginModal = document.getElementById('loginModal');
        const adminLoginBtn = document.getElementById('adminLoginBtn');
        const adminLoginForm = document.getElementById('adminLoginForm');
        const dashboard = document.getElementById('dashboard');
        const closeBtn = document.querySelector('.close');
        const loginError = document.getElementById('loginError');

        // إعداد الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadProductsToPage();
            setupAdminEvents();
            checkAdminSession();
            setupSearchFunction();
        });

        // إعداد أحداث لوحة التحكم
        function setupAdminEvents() {
            // فتح نافذة تسجيل الدخول
            adminLoginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                loginModal.style.display = 'block';
            });

            // إغلاق النافذة
            closeBtn.addEventListener('click', () => {
                loginModal.style.display = 'none';
                loginError.textContent = '';
            });

            // إغلاق النافذة عند النقر خارجها
            window.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    loginModal.style.display = 'none';
                    loginError.textContent = '';
                }
            });

            // معالج تسجيل الدخول
            adminLoginForm.addEventListener('submit', handleAdminLogin);

            // زر تسجيل الخروج
            document.getElementById('logout-btn').addEventListener('click', handleLogout);

            // معالج إضافة منتج
            const productForm = document.getElementById('product-form');
            if (productForm) {
                productForm.addEventListener('submit', handleAddProduct);
            }
        }

        // معالج تسجيل دخول المدير
        function handleAdminLogin(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === adminCredentials.username && password === adminCredentials.password) {
                localStorage.setItem('adminLoggedIn', 'true');
                loginModal.style.display = 'none';
                dashboard.style.display = 'block';
                document.body.style.overflow = 'hidden';
                loginError.textContent = '';
                loadAdminProducts();
            } else {
                loginError.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        }

        // معالج تسجيل الخروج
        function handleLogout() {
            localStorage.removeItem('adminLoggedIn');
            dashboard.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // فحص جلسة المدير
        function checkAdminSession() {
            if (localStorage.getItem('adminLoggedIn') === 'true') {
                dashboard.style.display = 'block';
                document.body.style.overflow = 'hidden';
                loadAdminProducts();
            }
        }
        
        // معالج إضافة منتج جديد
        function handleAddProduct(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const newProduct = {
                id: Date.now().toString(),
                name: formData.get('product-name') || document.getElementById('product-name').value,
                price: parseFloat(formData.get('product-price') || document.getElementById('product-price').value),
                category: formData.get('product-category') || document.getElementById('product-category').value,
                image: formData.get('product-image') || document.getElementById('product-image').value,
                description: formData.get('product-description') || document.getElementById('product-description').value || 'وصف المنتج',
                featured: Math.random() > 0.7 // عشوائي لجعل بعض المنتجات مميزة
            };

            // التحقق من صحة البيانات
            if (!newProduct.name || !newProduct.price || !newProduct.category || !newProduct.image) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إضافة المنتج إلى القائمة
            products.push(newProduct);
            localStorage.setItem('storeProducts', JSON.stringify(products));

            // تحديث العرض
            loadProductsToPage();
            loadAdminProducts();

            // مسح النموذج
            e.target.reset();

            alert('تم إضافة المنتج بنجاح!');
        }

        // تحميل المنتجات إلى الصفحة الرئيسية
        function loadProductsToPage() {
            const productsGrid = document.querySelector('.products-grid');
            if (!productsGrid) return;

            // مسح المنتجات الموجودة
            productsGrid.innerHTML = '';

            // إضافة المنتجات الجديدة
            products.forEach(product => {
                const productCard = createProductCard(product);
                productsGrid.appendChild(productCard);
            });

            // تحديث شريط المنتجات أيضاً
            updateProductsSlider();
        }

        // إنشاء بطاقة منتج
        function createProductCard(product) {
            const card = document.createElement('div');
            card.className = 'product-card';

            card.innerHTML = `
                ${product.featured ? '<div class="featured-badge">مميز</div>' : ''}
                <div class="product-img">
                    <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة+غير+متوفرة'">
                </div>
                <div class="view-details">
                    <a href="#" class="view-details-btn">عرض التفاصيل</a>
                </div>
                <div class="product-info">
                    <h3>${product.name}</h3>
                    <div class="product-price">${product.price} ر.س</div>
                </div>
            `;

            return card;
        }

        // تحديث شريط المنتجات
        function updateProductsSlider() {
            const slider = document.querySelector('.slider');
            if (!slider) return;

            // الاحتفاظ ببعض المنتجات الأصلية وإضافة الجديدة
            const sliderProducts = products.slice(0, 5); // أول 5 منتجات

            // مسح المحتوى الحالي
            slider.innerHTML = '';

            sliderProducts.forEach(product => {
                const slide = document.createElement('div');
                slide.className = 'slide';

                slide.innerHTML = `
                    <div class="product-img">
                        <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x250?text=صورة+غير+متوفرة'">
                    </div>
                    <div class="product-info">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="product-price">${product.price} ر.س</div>
                        <button class="btn">أضف إلى السلة</button>
                    </div>
                `;

                slider.appendChild(slide);
            });
        }

        // تحميل المنتجات في لوحة التحكم
        function loadAdminProducts() {
            const tableBody = document.querySelector('.admin-table tbody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            products.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><img src="${product.image}" alt="${product.name}" style="width:50px;height:50px;object-fit:cover;" onerror="this.src='https://via.placeholder.com/50x50?text=صورة'"></td>
                    <td>${product.name}</td>
                    <td>${product.category}</td>
                    <td>${product.price} ر.س</td>
                    <td>
                        <button class="action-btn edit-btn" onclick="editProduct('${product.id}')">تعديل</button>
                        <button class="action-btn delete-btn" onclick="deleteProduct('${product.id}')">حذف</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // حذف منتج
        function deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                products = products.filter(p => p.id !== productId);
                localStorage.setItem('storeProducts', JSON.stringify(products));
                loadProductsToPage();
                loadAdminProducts();
            }
        }

        // تعديل منتج (وظيفة أساسية)
        function editProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (product) {
                // ملء النموذج بالبيانات الحالية
                document.getElementById('product-name').value = product.name;
                document.getElementById('product-price').value = product.price;
                document.getElementById('product-category').value = product.category;
                document.getElementById('product-image').value = product.image;
                if (document.getElementById('product-description')) {
                    document.getElementById('product-description').value = product.description;
                }

                // حذف المنتج القديم عند الحفظ
                deleteProduct(productId);
            }
        }

        // إعداد وظيفة البحث
        function setupSearchFunction() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            if (searchInput && searchBtn) {
                // البحث عند النقر على الزر
                searchBtn.addEventListener('click', performSearch);

                // البحث عند الضغط على Enter
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });

                // البحث المباشر أثناء الكتابة
                searchInput.addEventListener('input', performSearch);
            }
        }

        // تنفيذ البحث
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const productsGrid = document.querySelector('.products-grid');

            if (!productsGrid) return;

            // مسح المنتجات الحالية
            productsGrid.innerHTML = '';

            // تصفية المنتجات حسب البحث
            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.category.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );

            // عرض النتائج
            if (filteredProducts.length === 0 && searchTerm !== '') {
                productsGrid.innerHTML = '<div class="no-results">لم يتم العثور على منتجات مطابقة للبحث</div>';
            } else {
                const productsToShow = searchTerm === '' ? products : filteredProducts;
                productsToShow.forEach(product => {
                    const productCard = createProductCard(product);
                    productsGrid.appendChild(productCard);
                });
            }
        }

        // معلومات الدخول للمدير:
        // اسم المستخدم: admin
        // كلمة المرور: admin123
    </script>
</body>
</html>